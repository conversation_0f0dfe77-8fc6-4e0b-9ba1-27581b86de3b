import React from 'react';
import { useCVStore } from '../../store/cvStore';
import { 
  User, FileText, Briefcase, GraduationCap, 
  Star, Heart, BookUser, FolderGit2, Check, X,
  LucideIcon
} from 'lucide-react';
import DownloadPdfButton from '../PDF/DownloadPdfButton';
import { SectionVisibility } from '../../types/cv.types';

interface SectionItem {
  id: string;
  label: string;
  icon: LucideIcon;
  toggleable?: boolean;
}

const sections: SectionItem[] = [
  { id: 'personalInfo', label: 'Personal Info', icon: User },
  { id: 'summary', label: 'Summary', icon: FileText, toggleable: true },
  { id: 'workExperience', label: 'Work Experience', icon: Briefcase, toggleable: true },
  { id: 'education', label: 'Education', icon: GraduationCap, toggleable: true },
  { id: 'projects', label: 'Projects', icon: FolderGit2, toggleable: true },
  { id: 'skills', label: 'Skills', icon: Star, toggleable: true },
  { id: 'interests', label: 'Interests', icon: Heart, toggleable: true },
  { id: 'references', label: 'References', icon: BookUser, toggleable: true },
];

const Sidebar: React.FC = (): React.ReactElement => {
  const { activeSection, setActiveSection, visibility, toggleSectionVisibility } = useCVStore();

  return (
    <div className="w-full lg:w-64 bg-white shadow-md h-auto lg:h-full p-4 overflow-y-auto">
      <h2 className="text-xl font-bold mb-6 text-gray-800">CV Builder</h2>
      
      <div className="space-y-1">
        {sections.map((section) => (
          <div key={section.id} className="flex items-center">
            <button
              className={`flex items-center w-full p-3 rounded-lg transition-colors duration-200 ${
                activeSection === section.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              onClick={(): void => setActiveSection(section.id)}
              type="button"
              aria-label={`Edit ${section.label} section`}
              aria-pressed={activeSection === section.id}
            >
              <section.icon className="w-5 h-5 mr-3" aria-hidden="true" />
              <span className="font-medium">{section.label}</span>
            </button>
            
            {section.toggleable && (
              <button
                className={`ml-2 p-2 rounded-full transition-colors duration-200 ${
                  visibility[section.id as keyof SectionVisibility]
                    ? 'text-green-600 hover:bg-green-100'
                    : 'text-red-600 hover:bg-red-100'
                }`}
                onClick={(): void => toggleSectionVisibility(section.id as keyof SectionVisibility)}
                title={`${visibility[section.id as keyof SectionVisibility] ? 'Hide' : 'Show'} section in CV`}
                type="button"
                aria-label={`${visibility[section.id as keyof SectionVisibility] ? 'Hide' : 'Show'} ${section.label} section in CV`}
                aria-pressed={visibility[section.id as keyof SectionVisibility]}
              >
                {visibility[section.id as keyof SectionVisibility] ? (
                  <Check className="w-4 h-4" aria-hidden="true" />
                ) : (
                  <X className="w-4 h-4" aria-hidden="true" />
                )}
              </button>
            )}
          </div>
        ))}
      </div>
      
      <div className="mt-auto pt-6">
        <DownloadPdfButton 
          className="w-full flex items-center justify-center py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          id="downloadPdfBtn"
        />
      </div>
    </div>
  );
};

export default Sidebar;
