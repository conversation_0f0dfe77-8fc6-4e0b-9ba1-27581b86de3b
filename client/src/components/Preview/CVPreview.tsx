import React from 'react';
import { useCVStore } from '../../store/cvStore';
import PersonalInfoPreview from './Sections/PersonalInfoPreview';
import SummaryPreview from './Sections/SummaryPreview';
import WorkExperiencePreview from './Sections/WorkExperiencePreview';
import EducationPreview from './Sections/EducationPreview';
import ProjectsPreview from './Sections/ProjectsPreview';
import SkillsPreview from './Sections/SkillsPreview';
import InterestsPreview from './Sections/InterestsPreview';
import ReferencesPreview from './Sections/ReferencesPreview';
import DownloadPdfButton from '../PDF/DownloadPdfButton';

const CVPreview: React.FC = (): React.ReactElement => {
  const { data, visibility } = useCVStore();
  const hasData = Object.values(data).some(
    (value) => 
      (typeof value === 'string' && value.length > 0) || 
      (Array.isArray(value) && value.length > 0) ||
      (typeof value === 'object' && value !== null && Object.values(value).some(v => Boolean(v) && typeof v === 'string' && v.length > 0))
  );

  if (!hasData) {
    return (
      <div className="h-full flex items-center justify-center p-6 bg-gray-50 rounded-lg">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-700 mb-2">CV Preview</h3>
          <p className="text-gray-500">
            Start filling out your information to see a preview of your CV here.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden h-full">
      <div className="overflow-y-auto h-full">
        <div className="p-6 min-h-full bg-gray-50 relative">
          <div className="absolute top-4 right-4 z-10">
            <DownloadPdfButton 
              className="flex items-center py-1.5 px-3 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors duration-200"
            />
          </div>
          
          <div id="cv-preview" className="max-w-[800px] mx-auto bg-white shadow-sm p-8 rounded-md">
            <PersonalInfoPreview personalInfo={data.personalInfo} />
            
            {visibility.summary && data.summary && (
              <SummaryPreview summary={data.summary} />
            )}
            
            {visibility.workExperience && data.workExperience.length > 0 && (
              <WorkExperiencePreview experiences={data.workExperience} />
            )}
            
            {visibility.education && data.education.length > 0 && (
              <EducationPreview education={data.education} />
            )}
            
            {visibility.projects && data.projects.length > 0 && (
              <ProjectsPreview projects={data.projects} />
            )}
            
            {visibility.skills && data.skills.length > 0 && (
              <SkillsPreview skills={data.skills} />
            )}
            
            {visibility.interests && data.interests.length > 0 && (
              <InterestsPreview interests={data.interests} />
            )}
            
            {visibility.references && data.references.length > 0 && (
              <ReferencesPreview references={data.references} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CVPreview;
