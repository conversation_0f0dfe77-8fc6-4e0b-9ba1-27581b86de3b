import React from 'react';
import { useCVStore } from './store/cvStore';
import Sidebar from './components/Layout/Sidebar';
import TemplatePreview from './components/Preview/TemplatePreview';
import PersonalInfoForm from './components/Form/PersonalInfoForm';
import SummaryForm from './components/Form/SummaryForm';
import WorkExperienceForm from './components/Form/WorkExperienceForm';
import EducationForm from './components/Form/EducationForm';
import ProjectsForm from './components/Form/ProjectsForm';
import SkillsForm from './components/Form/SkillsForm';
import InterestsForm from './components/Form/InterestsForm';
import ReferencesForm from './components/Form/ReferencesForm';

const App: React.FC = (): React.ReactElement => {
  const { activeSection } = useCVStore();

  const renderActiveForm = (): React.ReactNode => {
    switch (activeSection) {
      case 'personalInfo':
        return <PersonalInfoForm />;
      case 'summary':
        return <SummaryForm />;
      case 'workExperience':
        return <WorkExperienceForm />;
      case 'education':
        return <EducationForm />;
      case 'projects':
        return <ProjectsForm />;
      case 'skills':
        return <SkillsForm />;
      case 'interests':
        return <InterestsForm />;
      case 'references':
        return <ReferencesForm />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left Sidebar */}
          <div className="lg:w-1/4">
            <Sidebar />
          </div>

          {/* Middle Form Section */}
          <div className="lg:w-1/3 bg-white rounded-lg shadow-md p-6">
            {renderActiveForm()}
          </div>

          {/* Right Preview Section */}
          <div className="lg:w-5/12">
            <TemplatePreview />
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
