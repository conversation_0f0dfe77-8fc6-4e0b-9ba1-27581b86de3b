# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a CV Builder application with a React frontend and Express.js backend, using Bun as the runtime. The application allows users to create and edit CVs through a form interface and generate PDF downloads.

## Architecture

### Frontend (client/)
- **Framework**: React 19 with TypeScript
- **State Management**: Zustand with persistence
- **Styling**: Tailwind CSS 4.x
- **Rich Text**: Quill editor for descriptions
- **Build Tool**: Vite
- **Package Manager**: Bun

### Backend (server/)
- **Framework**: Express.js with TypeScript
- **Runtime**: Bun
- **PDF Generation**: Puppeteer
- **Security**: Helmet, CORS, rate limiting
- **Logging**: Winston
- **Testing**: Bun test framework

### Key Data Flow
1. CV data is managed in Zustand store (`client/src/store/cvStore.ts`)
2. Forms update the store with sectioned data (PersonalInfo, WorkExperience, etc.)
3. PDF generation sends data to `/api/generate-pdf` endpoint
4. Server uses <PERSON><PERSON>pet<PERSON> to convert HTML templates to PDF
5. Types are shared but maintained separately in client and server

## Development Commands

### Client Development
```bash
cd client
bun install
bun run dev          # Start development server
bun run build        # Build for production
bun run lint         # Run ESLint
bun run preview      # Preview production build
```

### Server Development
```bash
cd server
bun install
bun run dev          # Start with hot reload
bun run build        # Build TypeScript
bun run start        # Start production server
bun run test         # Run tests
bun run test:watch   # Run tests in watch mode
bun run test:coverage # Run tests with coverage
```

### Full Stack Development
```bash
cd server
bun run dev:full     # Build client and start server
bun run start:full   # Build client and start production server
```

## Code Architecture

### State Management
- **Zustand Store**: Centralized CV data with persistence (`client/src/store/cvStore.ts`)
- **Section-based Structure**: Data organized by CV sections (personalInfo, workExperience, education, etc.)
- **Visibility Control**: Toggle sections on/off for PDF generation

### Type System
- **Shared Interfaces**: Similar type definitions in both client and server
- **Client Types**: `client/src/types/cv.types.ts`
- **Server Types**: `server/src/types/cv.ts`
- **Note**: Types are maintained separately but should stay in sync

### PDF Generation Pipeline
1. **HTML Generation**: `server/src/utils/htmlGenerator.ts` converts CV data to HTML
2. **Template System**: Uses HTML templates in `server/templates/`
3. **Puppeteer Rendering**: Converts HTML to PDF with specific formatting
4. **Security**: Puppeteer runs with restricted sandbox settings

### Component Structure
- **Form Components**: Each CV section has its own form (`client/src/components/Form/`)
- **Preview Components**: Real-time preview of each section (`client/src/components/Preview/`)
- **Layout Components**: Sidebar navigation and main layout structure

### Server Architecture
- **Clean Architecture**: Separation of concerns with controllers, services, middleware
- **Dependency Injection**: Services injected into controllers
- **Error Handling**: Centralized error handling with custom error classes
- **Security Middleware**: Applied globally for CORS, rate limiting, and security headers

## Testing

### Server Tests
- **Unit Tests**: Services and utilities
- **Integration Tests**: API endpoints
- **Test Setup**: Uses Bun's built-in test runner
- **Coverage**: Available with `bun run test:coverage`

### Client Testing
- No test setup currently configured

## Common Patterns

### Adding New CV Sections
1. Update type definitions in both client and server
2. Add form component in `client/src/components/Form/`
3. Add preview component in `client/src/components/Preview/`
4. Update Zustand store with new actions
5. Update HTML generator to handle new section
6. Add section to sidebar navigation

### Error Handling
- **Client**: Form validation using react-hook-form
- **Server**: Custom error classes (`BadRequestError`, `InternalServerError`)
- **Logging**: Winston logger with different levels

### Security Considerations
- Rate limiting on API endpoints
- Input validation using express-validator
- Helmet for security headers
- Puppeteer runs with restricted permissions
- CORS configured for frontend-backend communication