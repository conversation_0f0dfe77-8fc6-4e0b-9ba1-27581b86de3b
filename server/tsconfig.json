{"compilerOptions": {"target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "outDir": "./dist", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "allowImportingTsExtensions": true, "noEmit": true, "noImplicitOverride": true, "noImplicitAny": false, "types": ["bun-types", "node"]}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist"]}