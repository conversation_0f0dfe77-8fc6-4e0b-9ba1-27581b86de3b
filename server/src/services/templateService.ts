import fs from 'fs/promises';
import path from 'path';
import { CVData, SectionVisibility } from '../types/cv.ts';
import { createAppLogger } from '../utils/logger.ts';
import { DataFilterService } from './dataFilterService.ts';

interface TemplateData {
  personalInfo: any;
  summary: string;
  workExperience: any[];
  education: any[];
  projects: any[];
  skills: any[];
  interests: any[];
  references: any[];
  visibility: SectionVisibility;
  helpers: {
    escapeHtml: (str: string | undefined) => string;
    formatDate: (dateStr?: string, defaultValue?: string) => string;
  };
}

/**
 * Template Service - Handles CV template processing
 * Supports placeholder replacement and conditional sections
 */
export class TemplateService {
  private logger = createAppLogger();
  private templatesPath: string;
  private templateCache = new Map<string, string>();
  private dataFilterService = new DataFilterService();

  constructor() {
    this.templatesPath = path.join(process.cwd(), 'templates');
  }

  /**
   * Utility function to escape HTML special characters
   */
  private escapeHtml(str: string | undefined): string {
    if (!str) return '';
    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  /**
   * Format date string
   */
  private formatDate(dateStr?: string, defaultValue = ''): string {
    if (!dateStr) return defaultValue;
    try {
      const date = new Date(dateStr);
      // Adjust for timezone to prevent off-by-one day errors
      const utcDate = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());
      return utcDate.toLocaleString('en-US', { month: 'long', year: 'numeric', timeZone: 'UTC' });
    } catch {
      return dateStr; // Return original string if format is invalid
    }
  }

  /**
   * Load template from file system
   */
  private async loadTemplate(templateName: string): Promise<string> {
    if (this.templateCache.has(templateName)) {
      return this.templateCache.get(templateName)!;
    }

    try {
      const templatePath = path.join(this.templatesPath, `${templateName}.html`);
      const template = await fs.readFile(templatePath, 'utf-8');
      
      // Cache the template for better performance
      this.templateCache.set(templateName, template);
      
      this.logger.info(`Template loaded: ${templateName}`);
      return template;
    } catch (error) {
      this.logger.error(`Failed to load template: ${templateName}`, { error });
      throw new Error(`Template not found: ${templateName}`);
    }
  }

  /**
   * Process template with data using simple placeholder replacement
   */
  private processTemplate(template: string, data: TemplateData): string {
    let processed = template;

    // Replace simple placeholders like {{personalInfo.firstName}}
    processed = processed.replace(/\{\{([^}]+)\}\}/g, (_, path) => {
      try {
        const value = this.getNestedValue(data, path.trim());
        return value != null ? String(value) : '';
      } catch {
        return '';
      }
    });

    // Process conditional sections {{#if condition}}...{{/if}}
    processed = processed.replace(/\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (_, condition, content) => {
      try {
        const shouldShow = this.evaluateCondition(data, condition.trim());
        return shouldShow ? content : '';
      } catch {
        return '';
      }
    });

    // Process loops {{#each array}}...{{/each}}
    processed = processed.replace(/\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g, (_, arrayPath, itemTemplate) => {
      try {
        const array = this.getNestedValue(data, arrayPath.trim());
        if (!Array.isArray(array)) return '';
        
        return array.map(item => {
          // Create a new data context with the current item
          const itemData = { ...data, this: item };
          return this.processTemplate(itemTemplate, itemData);
        }).join('');
      } catch {
        return '';
      }
    });

    return processed;
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    if (path === 'this') return obj.this;
    
    return path.split('.').reduce((current, key) => {
      if (current && typeof current === 'object') {
        return current[key];
      }
      return undefined;
    }, obj);
  }

  /**
   * Evaluate condition for conditional sections
   */
  private evaluateCondition(data: TemplateData, condition: string): boolean {
    try {
      // Handle simple existence checks
      if (!condition.includes(' ')) {
        const value = this.getNestedValue(data, condition);
        if (Array.isArray(value)) return value.length > 0;
        if (typeof value === 'string') return value.trim().length > 0;
        return Boolean(value);
      }

      // Handle array length checks like "workExperience.length > 0"
      const lengthMatch = condition.match(/^([^.]+)\.length\s*>\s*(\d+)$/);
      if (lengthMatch) {
        const [, arrayPath, threshold] = lengthMatch;
        const array = this.getNestedValue(data, arrayPath);
        return Array.isArray(array) && array.length > parseInt(threshold);
      }

      // Handle visibility checks like "visibility.summary"
      const visibilityMatch = condition.match(/^visibility\.(\w+)$/);
      if (visibilityMatch) {
        const [, section] = visibilityMatch;
        return Boolean(data.visibility[section as keyof SectionVisibility]);
      }

      return false;
    } catch {
      return false;
    }
  }

  /**
   * Get list of available templates
   */
  public async getAvailableTemplates(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.templatesPath);
      return files
        .filter(file => file.endsWith('.html'))
        .map(file => file.replace('.html', ''));
    } catch (error) {
      this.logger.error('Failed to list templates', { error });
      return [];
    }
  }

  /**
   * Render CV using specified template
   */
  public async renderCV(templateName: string, cvData: CVData, visibility: SectionVisibility): Promise<string> {
    try {
      const template = await this.loadTemplate(templateName);
      
      // Filter data based on visibility settings - this eliminates complex template conditions
      const filteredData = this.dataFilterService.filterCVData(cvData, visibility);
      
      const templateData: TemplateData = {
        personalInfo: filteredData.personalInfo,
        summary: filteredData.summary || '',
        workExperience: filteredData.workExperience || [],
        education: filteredData.education || [],
        projects: filteredData.projects || [],
        skills: filteredData.skills || [],
        interests: filteredData.interests || [],
        references: filteredData.references || [],
        visibility: {
          summary: filteredData.hasSummary,
          workExperience: filteredData.hasWorkExperience,
          education: filteredData.hasEducation,
          projects: filteredData.hasProjects,
          skills: filteredData.hasSkills,
          interests: filteredData.hasInterests,
          references: filteredData.hasReferences,
        },
        helpers: {
          escapeHtml: this.escapeHtml,
          formatDate: this.formatDate,
        }
      };

      const rendered = this.processTemplate(template, templateData);
      
      this.logger.info(`CV rendered successfully with template: ${templateName}`);
      return rendered;
    } catch (error) {
      this.logger.error(`Failed to render CV with template: ${templateName}`, { error });
      throw error;
    }
  }

  /**
   * Clear template cache (useful for development)
   */
  public clearCache(): void {
    this.templateCache.clear();
    this.logger.info('Template cache cleared');
  }
}