import { CVData, SectionVisibility } from '../types/cv.ts';

interface FilteredCVData {
  personalInfo: any;
  summary?: string;
  workExperience?: any[];
  education?: any[];
  projects?: any[];
  skills?: any[];
  interests?: any[];
  references?: any[];
  // Helper flags for templates
  hasPersonalInfo: boolean;
  hasSummary: boolean;
  hasWorkExperience: boolean;
  hasEducation: boolean;
  hasProjects: boolean;
  hasSkills: boolean;
  hasInterests: boolean;
  hasReferences: boolean;
}

/**
 * Data Filter Service - Processes CV data based on visibility settings
 * This eliminates the need for complex template conditions
 */
export class DataFilterService {
  
  /**
   * Filter CV data based on visibility settings and content existence
   */
  public filterCVData(data: CVData, visibility: SectionVisibility): FilteredCVData {
    const filtered: FilteredCVData = {
      personalInfo: data.personalInfo || {},
      hasPersonalInfo: this.hasPersonalInfoContent(data.personalInfo),
      hasSummary: false,
      hasWorkExperience: false,
      hasEducation: false,
      hasProjects: false,
      hasSkills: false,
      hasInterests: false,
      hasReferences: false,
    };

    // Summary - only include if visible and has content
    if (visibility.summary && this.hasStringContent(data.summary)) {
      filtered.summary = data.summary;
      filtered.hasSummary = true;
    }

    // Work Experience - only include if visible and has entries
    if (visibility.workExperience && this.hasArrayContent(data.workExperience)) {
      filtered.workExperience = data.workExperience;
      filtered.hasWorkExperience = true;
    }

    // Education - only include if visible and has entries
    if (visibility.education && this.hasArrayContent(data.education)) {
      filtered.education = data.education;
      filtered.hasEducation = true;
    }

    // Projects - only include if visible and has entries
    if (visibility.projects && this.hasArrayContent(data.projects)) {
      filtered.projects = data.projects;
      filtered.hasProjects = true;
    }

    // Skills - only include if visible and has entries
    if (visibility.skills && this.hasArrayContent(data.skills)) {
      filtered.skills = data.skills;
      filtered.hasSkills = true;
    }

    // Interests - only include if visible and has entries
    if (visibility.interests && this.hasArrayContent(data.interests)) {
      filtered.interests = data.interests;
      filtered.hasInterests = true;
    }

    // References - only include if visible and has entries
    if (visibility.references && this.hasArrayContent(data.references)) {
      filtered.references = data.references;
      filtered.hasReferences = true;
    }

    return filtered;
  }

  /**
   * Check if personal info has meaningful content
   */
  private hasPersonalInfoContent(personalInfo: any): boolean {
    if (!personalInfo) return false;
    
    // At minimum, should have first name or last name
    return this.hasStringContent(personalInfo.firstName) || 
           this.hasStringContent(personalInfo.lastName);
  }

  /**
   * Check if string has meaningful content
   */
  private hasStringContent(value: any): boolean {
    return typeof value === 'string' && value.trim().length > 0;
  }

  /**
   * Check if array has meaningful content
   */
  private hasArrayContent(value: any): boolean {
    return Array.isArray(value) && value.length > 0;
  }
}