---
trigger: manual
---

Project Structure
Use a clear directory structure:
server/
├── src/
│   ├── controllers/    # Request handlers
│   ├── services/       # Business logic
│   ├── models/         # Data models
│   ├── repositories/   # Data access
│   ├── middleware/     # Custom middleware
│   ├── routes/         # Route definitions
│   ├── utils/          # Utility functions
│   ├── validators/     # Input validation
│   ├── config/         # Configuration
│   ├── types/          # Type definitions
│   └── app.js          # App initialization
├── tests/              # Test files
├── .env.example        # Example environment variables
├── .gitignore
├── package.json
└── server.js           # Entry point
Use TypeScript for type safety and better IDE support
SOLID Principles Implementation
Single Responsibility Principle (SRP):
Each class/module should have only one reason to change
Separate controllers, services, and repositories
Open/Closed Principle (OCP):
Use middleware pattern to extend functionality
Create extensible base classes for common operations
Liskov Substitution Principle (LSP):
Child classes should be substitutable for their parent classes
Use interfaces for consistent contracts
Interface Segregation Principle (ISP):
Create small, focused interfaces rather than large ones
Define specific interfaces for repositories, services, etc.
Dependency Inversion Principle (DIP):
Depend on abstractions, not concretions
Use dependency injection for services and repositories
Security Rules
Environment Configuration:
Store sensitive data in environment variables
Never commit .env files to version control
Input Validation:
Validate all input data using libraries like Joi or express-validator
Sanitize inputs to prevent injection attacks
Authentication & Authorization:
Implement proper JWT authentication
Use role-based access control for endpoints
Store hashed passwords only (use bcrypt)
Security Headers:
Use helmet middleware to set security headers
Implement CORS policies appropriately
Rate Limiting:
Implement rate limiting to prevent brute force attacks
Use express-rate-limit or similar middleware
Scalability & Reliability
Error Handling:
Create a centralized error handling middleware
Use custom error classes with appropriate HTTP status codes
Include informative but safe error messages
Logging:
Implement structured logging with winston or pino
Log all requests, responses, and errors
Use different log levels for development and production
Performance:
Use compression middleware
Implement proper caching strategies
Consider pagination for list endpoints
Testing:
Write unit tests for all business logic
Implement integration tests for API endpoints
Use a CI/CD pipeline for automated testing
Code Quality & Best Practices
Documentation:
Document all APIs using OpenAPI/Swagger
Include JSDoc comments for functions and classes
Code Consistency:
Use ESLint and Prettier for code style enforcement
Establish naming conventions (camelCase for variables, PascalCase for classes)
Versioning:
Version your APIs (e.g., /api/v1/)
Follow semantic versioning for your package
Dependency Management:
Regularly update dependencies
Lock dependency versions in package.json
Avoid unnecessary dependencies
Package Management:
Use bun as package manager